<?php

declare(strict_types=1);

namespace App\Http\Controllers;

use App\Models\OneTimePassword;
use App\Models\RegistrationRequest;
use App\Models\Sender;
use App\Models\User;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;

class RegistrationRequestController
{
    /**
     * @api {post} /api/registration-request Registration Request
     *
     * @unauthenticated
     *
     * @bodyParam name required string
     * @bodyParam phone required string
     * @bodyParam email nullable string
     * @bodyParam city nullable string
     * @bodyParam company nullable string
     * @bodyParam service_type nullable string
     * @bodyParam have_smsapi nullable string [yes, no, developing]
     * @bodyParam target nullable string [OTP, Campaign, Notification]
     * @bodyParam system_type nullable string
     * @bodyParam note nullable string
     *
     * @response json
     *
     * @group Registration Request
     */
    public function register(Request $request): JsonResponse
    {
        /** @var array{'name': string, 'phone': string, 'email':string|null, 'city': string|null, 'company': string|null, 'category': string|null, 'have_smsapi': string|null, 'target': string|null, 'system_type': string|null, 'notes': string|null} $validatedData */
        $validatedData = Validator::make(
            [
                'name' => $request->name,
                'phone' => $request->phone,
                'email' => $request->email,
                'city' => $request->city,
                'company' => $request->company,
                'category' => $request->category,
                'have_smsapi' => $request->have_smsapi,
                'target' => $request->target,
                'system_type' => $request->system_type,
                'notes' => $request->notes,
            ],
            [
                'name' => 'required|string',
                'phone' => 'required|string',
                'email' => 'nullable|email',
                'city' => 'nullable|string',
                'company' => 'nullable|string',
                'category' => 'nullable|string',
                'have_smsapi' => 'nullable|string',
                'target' => 'nullable|array',
                'system_type' => 'nullable|string',
                'notes' => 'nullable|string',
            ],
        )->validate();

        if (User::where('email', '=', $validatedData['email'])->count() > 0 || RegistrationRequest::where('email', '=', $validatedData['email'])->count() > 0) {
            return response()->json([
                'message' => 'The email address is already exists.',
            ], 409);
        }

        RegistrationRequest::create([
            'name' => $validatedData['name'],
            'phone' => $validatedData['phone'],
            'email' => $validatedData['email'],
            'city' => $validatedData['city'],
            'company' => $validatedData['company'],
            'category' => $validatedData['category'],
            'have_smsapi' => $validatedData['have_smsapi'],
            'target' => $validatedData['target'],
            'system_type' => $validatedData['system_type'],
            'notes' => $validatedData['notes'],
        ]);

        return response()->json([
            'message' => 'The request has been registered successfully.',
        ], 201);
    }

    public function verifyPhoneNumber(Request $request): JsonResponse
    {
        $validatedData = Validator::make(
            [
                'phone' => $request->phone,
            ],
            [
                'phone' => 'required|exists:registration_requests,phone',
            ],
        )->validate();

        // create message and send otp

        $sender = Sender::where('sender', 'Lamah')->firstOrFail();

        $otp = OneTimePassword::create([
            'expiration_period' => 1,
            'length' => 4,
            'code' => random_int(1000, 9999),
            'message_id' => null,
        ]);

        return response()->json([
            'message' => 'The phone number is valid.',
        ], 200);

    }
}
