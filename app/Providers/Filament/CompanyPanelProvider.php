<?php

declare(strict_types=1);

namespace App\Providers\Filament;

use App\Http\Middleware\ApplyTenantScopes;
use App\Models\Company;
use Exception;
use Filament\FontProviders\LocalFontProvider;
use Filament\Http\Middleware\Authenticate;
use Filament\Http\Middleware\DisableBladeIconComponents;
use Filament\Http\Middleware\DispatchServingFilamentEvent;
use Filament\Pages;
use Filament\Panel;
use Filament\PanelProvider;
use Filament\Support\Colors\Color;
use Filament\View\PanelsRenderHook;
use Filament\Widgets;
use Illuminate\Cookie\Middleware\AddQueuedCookiesToResponse;
use Illuminate\Cookie\Middleware\EncryptCookies;
use Illuminate\Foundation\Http\Middleware\VerifyCsrfToken;
use Illuminate\Routing\Middleware\SubstituteBindings;
use Illuminate\Session\Middleware\AuthenticateSession;
use Illuminate\Session\Middleware\StartSession;
use Illuminate\View\Middleware\ShareErrorsFromSession;
use Livewire\Livewire;

final class CompanyPanelProvider extends PanelProvider
{
    /**
     * @throws Exception
     */
    public function panel(Panel $panel): Panel
    {
        return $panel
            ->id('company')
            ->path('company')
            ->tenant(Company::class, ownershipRelationship: 'company')
            ->login()
            ->brandLogo(asset('logo/eshaar-01.svg'))
            ->darkModeBrandLogo(asset('logo/eshaar-02.svg'))
            ->favicon('logo/eshaar-01.svg')
            ->font(
                'sst-arabic',
                url: asset(asset('fonts/font.css')),
                provider: LocalFontProvider::class,
            )
            ->colors([
                'primary' => [
                    50 => '#fff8e1',
                    100 => '#ffecb3',
                    200 => '#ffe082',
                    300 => '#ffd54f',
                    400 => '#ffca28',
                    500 => '#ffc10e', // base color
                    600 => '#e0a900',
                    700 => '#b38700',
                    800 => '#866400',
                    900 => '#594200',
                    950 => '#332700',
                ],
            ])
            ->discoverResources(
                in: app_path('Filament/Company/Resources'),
                for: 'App\\Filament\\Company\\Resources',
            )
            ->discoverPages(
                in: app_path('Filament/Company/Pages'),
                for: 'App\\Filament\\Company\\Pages',
            )
            ->pages([Pages\Dashboard::class])
            ->discoverWidgets(
                in: app_path('Filament/Company/Widgets'),
                for: 'App\\Filament\\Company\\Widgets',
            )
            ->widgets([
                Widgets\AccountWidget::class,
                Widgets\FilamentInfoWidget::class,
            ])
            ->middleware([
                EncryptCookies::class,
                AddQueuedCookiesToResponse::class,
                StartSession::class,
                AuthenticateSession::class,
                ShareErrorsFromSession::class,
                VerifyCsrfToken::class,
                SubstituteBindings::class,
                DisableBladeIconComponents::class,
                DispatchServingFilamentEvent::class,
            ])
            ->authMiddleware([Authenticate::class])
            ->viteTheme('resources/css/filament/company/theme.css')
            ->plugins([
                \BezhanSalleh\FilamentShield\FilamentShieldPlugin::make(),
            ])
            ->spa()
            ->renderHook(
                PanelsRenderHook::GLOBAL_SEARCH_BEFORE,
                fn () => Livewire::mount('current-balance'),
            )
            ->globalSearch(false)
            ->tenantMiddleware([
                ApplyTenantScopes::class,
            ], isPersistent: true);
    }
}
