{"name": "laravel/laravel", "type": "project", "description": "The skeleton application for the Laravel framework.", "keywords": ["laravel", "framework"], "license": "MIT", "require": {"php": "^8.4", "ext-curl": "*", "ext-iconv": "*", "bezhansalleh/filament-shield": "^3.3.5", "cweagans/composer-patches": "^1.7.3", "enqueue/amqp-bunny": "^0.10.25", "filament/filament": "^3.3.12", "filament/spatie-laravel-media-library-plugin": "^3.3.12", "knuckleswtf/scribe": "^5.2", "laravel/framework": "^11.44.2", "laravel/sanctum": "^4.0.8", "laravel/tinker": "^2.10.1", "maatwebsite/excel": "^3.1.64", "pxlrbt/filament-activity-log": "^1.1.9", "react/socket": "^1.16", "spatie/laravel-activitylog": "^4.10.1", "spatie/laravel-permission": "^6.17.0", "staudenmeir/eloquent-has-many-deep": "^1.20.7", "stechstudio/filament-impersonate": "^3.16", "vladimir-yuldashev/laravel-queue-rabbitmq": "^14.1"}, "require-dev": {"barryvdh/laravel-debugbar": "^3.15.4", "fakerphp/faker": "^1.24.1", "larastan/larastan": "^3.3.1", "laravel/pail": "^1.2.2", "laravel/pint": "^1.22.0", "laravel/sail": "^1.41", "mockery/mockery": "^1.6.12", "nunomaduro/collision": "^8.8.0", "pestphp/pest": "^3.8.2", "pestphp/pest-plugin-laravel": "^3.2", "pestphp/pest-plugin-livewire": "^3.0", "pestphp/pest-plugin-type-coverage": "^3.5.1", "projektgopher/whisky": "^0.7.4", "rector/rector": "^2.0.11", "symplify/vendor-patches": "^11.4.1"}, "autoload": {"psr-4": {"App\\": "app/", "Database\\Factories\\": "database/factories/", "Database\\Seeders\\": "database/seeders/"}}, "autoload-dev": {"psr-4": {"Tests\\": "tests/"}}, "scripts": {"post-autoload-dump": ["Illuminate\\Foundation\\ComposerScripts::postAutoloadDump", "@php artisan package:discover --ansi", "@php artisan filament:upgrade"], "post-update-cmd": ["@php artisan vendor:publish --tag=laravel-assets --ansi --force", "whisky update"], "post-root-package-install": ["@php -r \"file_exists('.env') || copy('.env.example', '.env');\""], "post-create-project-cmd": ["@php artisan key:generate --ansi", "@php -r \"file_exists('database/database.sqlite') || touch('database/database.sqlite');\"", "@php artisan migrate --graceful --ansi"], "dev": ["Composer\\Config::disableProcessTimeout", "npx concurrently -c \"#93c5fd,#c4b5fd,#fb7185,#fdba74\" \"php artisan serve\" \"php artisan queue:listen --tries=1\" \"php artisan pail --timeout=0\" \"npm run dev\" --names=server,queue,logs,vite"], "lint": "pint", "refactor": "rector", "phpstan": "vendor/bin/phpstan -c phpstan.neon", "test:type-coverage": "pest --type-coverage --compact --min=100 --memory-limit=2G", "test:lint": "pint --test", "test:unit": ["@putenv XDEBUG_MODE=coverage", "pest --parallel --coverage --exactly=100 --compact", "@putenv XDEBUG_MODE=off"], "test:types": "phpstan --memory-limit=2G", "test:refactor": "rector --dry-run", "test": ["@test:type-coverage", "@test:types", "@test:lint", "@test:refactor", "@test:unit"]}, "extra": {"patches": {}, "laravel": {"dont-discover": []}}, "config": {"optimize-autoloader": true, "preferred-install": "dist", "sort-packages": true, "allow-plugins": {"cweagans/composer-patches": true, "pestphp/pest-plugin": true, "php-http/discovery": true}}, "minimum-stability": "stable", "prefer-stable": true}