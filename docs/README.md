# Lamah SMS Gateway API Documentation

## Overview

Welcome to the Lamah SMS Gateway API documentation. This API allows you to integrate SMS messaging capabilities into your applications, including sending individual and bulk SMS messages, managing OTP verification, and more.

## Features

- **SMS Messaging**: Send individual, bulk, and template-based SMS messages
- **OTP Services**: Initiate and verify one-time passwords
- **Project Management**: View project details, balance, and consumption
- **Contact Management**: Manage contact groups for bulk messaging
- **Registration & Contact**: Submit registration requests and contact forms

## API Base URL

All API requests should be made to:

```
https://your-sms-gateway-domain.com/api
```

## Authentication

The Lamah SMS Gateway API uses Laravel Sanctum for authentication. Most endpoints require authentication via API tokens. See the [Authentication](authentication.md) section for details on how to obtain and use API tokens.

## Rate Limiting

Some endpoints, particularly OTP-related ones, have rate limiting applied to prevent abuse. See the [Rate Limiting](guides/rate-limiting.md) guide for more information.

## IP Whitelisting

For enhanced security, the API supports IP whitelisting. Only requests from whitelisted IP addresses will be processed. See the [IP Whitelisting](guides/ip-whitelisting.md) guide for details.

## OpenAPI Specification

This API is documented using the OpenAPI specification (v3.0.1). You can access the specification at:

```
https://your-sms-gateway-domain.com/docs/openapi.yaml
```

## Getting Started

To get started with the Lamah SMS Gateway API, follow these steps:

1. Register for an account and obtain your API credentials
2. Set up your project and whitelist your IP addresses
3. Explore the API endpoints in this documentation
4. Integrate the API into your application

For detailed information about specific endpoints, refer to the API Reference section.