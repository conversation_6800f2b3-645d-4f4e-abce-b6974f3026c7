# IP Whitelisting

## Overview

IP whitelisting is a security feature in the Lamah SMS Gateway API that restricts API access to only those requests coming from pre-approved IP addresses. This guide explains how IP whitelisting works, how to configure it, and best practices for managing your whitelisted IPs.

## How IP Whitelisting Works

When IP whitelisting is enabled for your project, the Lamah SMS Gateway API will check the source IP address of each incoming request. If the IP address is not on your whitelist, the request will be rejected with a `403 Forbidden` status code, even if the authentication token is valid.

## Benefits of IP Whitelisting

- **Enhanced Security**: Prevents unauthorized access even if API tokens are compromised
- **Reduced Attack Surface**: Limits the potential entry points for attackers
- **Compliance**: Helps meet security compliance requirements for sensitive operations
- **Audit Trail**: Makes it easier to trace API usage to specific servers or locations

## Managing Whitelisted IPs

You can manage your whitelisted IP addresses through the Lamah SMS Gateway dashboard or by contacting support. Each project can have its own set of whitelisted IPs.

### Adding IP Addresses

When adding IP addresses to your whitelist, you can specify:

- **Individual IPs**: Single IP addresses (e.g., `***********`)
- **IP Ranges**: Ranges of IP addresses using CIDR notation (e.g., `***********/24`)

### Removing IP Addresses

You can remove IP addresses from your whitelist at any time. Be cautious when removing IPs to avoid disrupting your services.

## Best Practices

1. **Start Restrictive**: Begin with a minimal set of whitelisted IPs and add more as needed

2. **Regular Audits**: Periodically review your whitelist to remove unused or unnecessary IPs

3. **Document Changes**: Maintain documentation of all changes to your whitelist, including the reason for each addition or removal

4. **Use Static IPs**: Ensure your applications use static IP addresses when making API calls

5. **Backup IPs**: Include backup or failover server IPs in your whitelist

6. **Test Before Deployment**: Test IP whitelisting in a development environment before enabling it in production

## Common Scenarios

### Cloud Hosting

If your application is hosted on a cloud platform, be aware that your IP address might change under certain conditions:

- **AWS**: Elastic IP addresses remain static, but default EC2 IPs can change when instances are stopped and started
- **Azure**: Static IP addresses can be assigned to resources but may require specific configuration
- **Google Cloud**: External IP addresses can be static or ephemeral

### Multiple Environments

If you have multiple environments (development, staging, production), consider:

- Maintaining separate whitelists for each environment
- Using different projects for different environments

### Mobile Applications

IP whitelisting is not suitable for direct API calls from mobile applications, as user IP addresses are unpredictable. Instead:

- Implement a server-side proxy that makes API calls on behalf of the mobile app
- Whitelist only the IPs of your proxy servers

## Troubleshooting

### 403 Forbidden Errors

If you're receiving `403 Forbidden` errors despite having valid authentication:

1. Verify that your current IP address is on the whitelist
2. Check if your IP address has changed (especially in cloud environments)
3. Ensure you're using the correct project credentials

### Emergency Access

If you need emergency access from a non-whitelisted IP:

1. Contact Lamah SMS Gateway support with your project details
2. Provide the new IP address that needs to be whitelisted
3. Explain the emergency situation

## Combining with Other Security Measures

IP whitelisting works best when combined with other security measures:

- **API Tokens**: Continue to use secure API tokens for authentication
- **HTTPS**: Always use HTTPS for API requests
- **Minimal Permissions**: Assign minimal necessary permissions to API tokens
- **Monitoring**: Monitor API usage for suspicious activity

By implementing IP whitelisting along with these other security measures, you can significantly enhance the security of your Lamah SMS Gateway integration.