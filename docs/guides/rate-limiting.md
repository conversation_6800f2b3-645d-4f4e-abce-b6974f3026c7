# Rate Limiting

## Overview

Rate limiting is a security feature implemented in the Lamah SMS Gateway API to prevent abuse and ensure fair usage of the service. This guide explains how rate limiting works, which endpoints are affected, and how to handle rate limit errors in your application.

## How Rate Limiting Works

The Lamah SMS Gateway API implements rate limiting based on the number of requests made within a specific time window. When you exceed the allowed number of requests, the API will return a `429 Too Many Requests` status code.

## Rate Limited Endpoints

The following endpoints have specific rate limits applied:

### OTP Endpoints

```http
POST /api/otp/initiate
```

This endpoint is rate-limited to prevent abuse of the OTP system. The rate limit is applied based on the recipient's phone number to prevent flooding a user with multiple OTP messages.

## Rate Limit Headers

When you make a request to a rate-limited endpoint, the response will include headers that provide information about your current rate limit status:

- `X-RateLimit-Limit`: The maximum number of requests allowed in the current time window
- `X-RateLimit-Remaining`: The number of requests remaining in the current time window
- `X-RateLimit-Reset`: The time (in seconds) until the rate limit window resets

## Handling Rate Limit Errors

When you exceed the rate limit, the API will return a `429 Too Many Requests` status code with a JSON response explaining the error. Your application should handle this error gracefully:

```json
{
  "message": "Too many requests. Please try again later.",
  "retry_after": 60
}
```

The `retry_after` field indicates the number of seconds you should wait before making another request.

## Best Practices for Handling Rate Limits

1. **Implement Exponential Backoff**: When you receive a rate limit error, wait for the suggested time and then retry with an exponential backoff strategy

2. **Client-Side Throttling**: Implement client-side throttling to prevent hitting rate limits in the first place

3. **Queue Requests**: Queue non-urgent requests and process them at a controlled rate

4. **Monitor Usage**: Monitor your API usage to identify patterns that might lead to rate limiting

5. **Optimize Requests**: Reduce the number of API calls by batching operations where possible

## Example: Handling Rate Limits in Code

```javascript
async function sendOTP(phoneNumber, retries = 3, delay = 1000) {
  try {
    const response = await fetch('https://your-sms-gateway-domain.com/api/otp/initiate', {
      method: 'POST',
      headers: {
        'Authorization': 'Bearer YOUR_API_TOKEN',
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        receiver: phoneNumber,
        // other required parameters
      })
    });
    
    if (response.status === 429 && retries > 0) {
      const data = await response.json();
      const retryAfter = data.retry_after || 60;
      
      console.log(`Rate limited. Retrying after ${retryAfter} seconds...`);
      
      // Wait for the suggested time plus some jitter
      await new Promise(resolve => setTimeout(resolve, retryAfter * 1000 + Math.random() * delay));
      
      // Retry with one less retry attempt and increased delay
      return sendOTP(phoneNumber, retries - 1, delay * 2);
    }
    
    return response.json();
  } catch (error) {
    console.error('Error sending OTP:', error);
    throw error;
  }
}
```

## Rate Limit Considerations

- **Different Limits for Different Endpoints**: Different endpoints may have different rate limits based on their purpose and resource requirements

- **Account-Based Limits**: Rate limits may vary based on your account type or subscription plan

- **IP-Based Limits**: Some rate limits may be applied based on the IP address making the request

- **Shared Limits**: If you're using the API from multiple applications or services, they may share the same rate limit

## Requesting Rate Limit Increases

If your use case requires higher rate limits, you can contact the Lamah SMS Gateway support team to discuss your needs. Be prepared to provide information about your use case, expected volume, and reasons for needing increased limits.