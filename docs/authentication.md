# Authentication

## Overview

The Lamah SMS Gateway API uses Laravel Sanctum for authentication. Most API endpoints require authentication via API tokens to ensure secure access to the SMS gateway services.

## API Tokens

API tokens are associated with Projects in the Lamah SMS Gateway system. Each project can have its own API token for authentication.

## Authentication Flow

1. Create or obtain a Project from the Lamah SMS Gateway dashboard
2. Generate an API token for your Project
3. Include the token in your API requests

## Using Your API Token

To authenticate your API requests, include your API token in the `Authorization` header using the Bearer token format:

```http
Authorization: Bearer YOUR_API_TOKEN
```

### Example Request with Authentication

```bash
curl -X POST https://your-sms-gateway-domain.com/api/sms/messages \
  -H "Authorization: Bearer YOUR_API_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "message": "Hello from Lamah SMS Gateway!",
    "sender": "LAMAH",
    "payment_type": "wallet",
    "receiver": "+1234567890"
  }'
```

## Protected Routes

All API endpoints under the following routes require authentication:

- `/api/sms/*` - SMS messaging endpoints
- `/api/otp/*` - OTP verification endpoints
- `/api/project/*` - Project management endpoints

## Public Routes

The following routes do not require authentication:

- `/api/plans` - Get available plans
- `/api/faq` - Get FAQ information
- `/api/registration-request` - Submit a registration request
- `/api/contact-us` - Submit a contact form

## Additional Security Measures

In addition to token authentication, the Lamah SMS Gateway API implements:

1. **IP Whitelisting**: Only requests from whitelisted IP addresses are processed
2. **Project Validation**: Ensures the project associated with the token is active and valid
3. **Rate Limiting**: Prevents abuse of the API, especially for OTP-related endpoints

See the [IP Whitelisting](guides/ip-whitelisting.md) and [Rate Limiting](guides/rate-limiting.md) guides for more information on these security features.