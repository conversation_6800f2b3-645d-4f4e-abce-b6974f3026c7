# Error Handling

## Overview

The Lamah SMS Gateway API uses conventional HTTP response codes to indicate the success or failure of API requests. In general, codes in the `2xx` range indicate success, codes in the `4xx` range indicate an error that resulted from the provided information (e.g., missing required parameters, invalid values), and codes in the `5xx` range indicate an error with the Lamah SMS Gateway servers.

## HTTP Status Codes

| Code | Description |
|------|-------------|
| 200  | OK - The request was successful |
| 201  | Created - The resource was successfully created |
| 400  | Bad Request - The request was invalid or cannot be served |
| 401  | Unauthorized - Authentication is required or has failed |
| 403  | Forbidden - The request is not allowed (e.g., IP not whitelisted) |
| 404  | Not Found - The requested resource does not exist |
| 422  | Unprocessable Entity - Validation errors |
| 429  | Too Many Requests - Rate limit exceeded |
| 500  | Internal Server Error - Something went wrong on the server |

## Error Response Format

When an error occurs, the API will return a JSON response with information about the error. The response will include an error message and, when applicable, additional details.

### Example Error Response

```json
{
  "message": "The given data was invalid.",
  "errors": {
    "message": [
      "The message field is required."
    ],
    "receiver": [
      "The receiver field is required."
    ]
  }
}
```

## Common Error Scenarios

### Authentication Errors

- **Invalid API Token**: If your API token is invalid or expired, you'll receive a 401 Unauthorized response.
- **IP Not Whitelisted**: If your IP address is not whitelisted, you'll receive a 403 Forbidden response.

### Validation Errors

- **Missing Required Fields**: If you omit required fields in your request, you'll receive a 422 Unprocessable Entity response with details about the missing fields.
- **Invalid Field Values**: If you provide invalid values for fields, you'll receive a 422 Unprocessable Entity response with details about the invalid values.

### Resource Errors

- **Insufficient Balance**: If you don't have enough balance to send messages, you'll receive a 400 Bad Request response with a message indicating insufficient balance.
- **Project Not Active**: If your project is not active, you'll receive a 400 Bad Request response with a message indicating the project is not active.

### Rate Limiting

- **OTP Rate Limit**: If you exceed the rate limit for OTP requests, you'll receive a 429 Too Many Requests response.

## Handling Errors

When building your integration with the Lamah SMS Gateway API, make sure to handle errors appropriately:

1. Check the HTTP status code of the response
2. Parse the error message and details from the response body
3. Implement appropriate error handling logic in your application
4. Consider implementing retry logic with exponential backoff for transient errors

For persistent errors, review your request parameters and ensure they meet the API requirements as documented in the API Reference section.