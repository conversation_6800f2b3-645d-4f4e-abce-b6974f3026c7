# Registration Request API

## Overview

The Registration Request API allows potential users to submit registration requests for the Lamah SMS Gateway service. This is typically the first step in the onboarding process for new users.

## Authentication

This endpoint does not require authentication and is publicly accessible.

## Endpoints

### Submit Registration Request

```http
POST /api/registration-request
```

Submit a registration request to use the Lamah SMS Gateway service.

#### Request Body

| Parameter    | Type   | Required | Description |
|--------------|--------|----------|-------------|
| name         | string | Yes      | Full name of the requester |
| phone        | string | Yes      | Phone number of the requester |
| email        | string | No       | Email address of the requester |
| city         | string | No       | City of the requester |
| company      | string | No       | Company name |
| service_type | string | No       | Type of service needed |
| have_smsapi  | string | No       | Whether the requester already has an SMS API: 'yes', 'no', or 'developing' |
| target       | string | No       | Target use case: 'OTP', 'Campaign', or 'Notification' |
| system_type  | string | No       | Type of system that will use the SMS gateway |
| note         | string | No       | Additional notes or requirements |

#### Example Request

```json
{
  "name": "<PERSON>",
  "phone": "+2519876543210",
  "email": "<EMAIL>",
  "city": "Addis Ababa",
  "company": "Tech Solutions Inc.",
  "service_type": "E-commerce Platform",
  "have_smsapi": "developing",
  "target": "OTP",
  "system_type": "Web Application",
  "note": "We need to implement SMS verification for our user registration process and would like to integrate with your gateway."
}
```

#### Example Response

```json
{
  "success": true,
  "message": "Your registration request has been received successfully. Our team will review your request and contact you within 24-48 hours.",
  "request_id": "reg_7d8e9f0a1b2c"
}
```

#### Response Format

A JSON object confirming the registration request has been received, typically including a success message and any next steps in the process.

## Target Use Cases

- **OTP**: One-Time Password verification for secure authentication
- **Campaign**: Marketing campaigns and promotional messages
- **Notification**: Transactional notifications and alerts

## SMS API Status

- **yes**: The requester already has an SMS API
- **no**: The requester does not have an SMS API
- **developing**: The requester is currently developing an SMS API

## Next Steps

After submitting a registration request:

1. The Lamah SMS Gateway team will review the request
2. If approved, the requester will be contacted with further instructions
3. Once onboarded, the user will receive API credentials to access the protected endpoints

## Error Handling

See the [Error Handling](../errors.md) section for details on error responses.

Common errors specific to registration request operations include:

- Missing required fields
- Invalid phone number format
- Duplicate registration request