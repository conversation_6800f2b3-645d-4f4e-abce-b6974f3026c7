# Contact Us API

## Overview

The Contact Us API allows users to submit contact requests or inquiries to the Lamah SMS Gateway team. This endpoint is useful for general inquiries, support requests, and feedback.

## Authentication

This endpoint does not require authentication and is publicly accessible.

## Endpoints

### Submit Contact Request

```http
POST /api/contact-us
```

Submit a contact request to the Lamah SMS Gateway team.

#### Request Body

| Parameter | Type   | Required | Description |
|-----------|--------|----------|-------------|
| name      | string | Yes      | Full name of the person making the inquiry |
| phone     | string | Yes      | Phone number of the person making the inquiry |
| subject   | string | Yes      | Subject of the inquiry |
| email     | string | Yes      | Email address for correspondence |
| message   | string | Yes      | Detailed message or inquiry content |

#### Example Request

```json
{
  "name": "<PERSON>",
  "phone": "+2519123456789",
  "subject": "API Integration Question",
  "email": "<EMAIL>",
  "message": "I'm interested in integrating your SMS gateway with our e-commerce platform. Could you provide more information about your API rate limits and bulk messaging capabilities?"
}
```

#### Example Response

```json
{
  "success": true,
  "message": "Thank you for contacting us. Your inquiry has been received and we will respond within 1-2 business days.",
  "reference": "inq_5a4b3c2d1e"
}
```

#### Response Format

```json
{
  "message": "string"
}
```

A JSON object confirming the contact request has been received, typically including a success message.

## Use Cases

- **General Inquiries**: Questions about the Lamah SMS Gateway service
- **Support Requests**: Technical support for existing users
- **Feedback**: Suggestions for improvement or feature requests
- **Partnership Opportunities**: Business partnership inquiries

## Response Time

The Lamah SMS Gateway team typically responds to contact requests within 1-2 business days. For urgent matters, users are encouraged to include relevant details in the subject line.

## Error Handling

See the [Error Handling](../errors.md) section for details on error responses.

Common errors specific to contact request operations include:

- Missing required fields
- Invalid email format
- Invalid phone number format