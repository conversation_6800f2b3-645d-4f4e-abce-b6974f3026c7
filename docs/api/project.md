# Project API

## Overview

The Project API allows you to retrieve information about your project, including details, balance, contacts, and consumption history. Projects are the primary entities in the Lamah SMS Gateway system that manage your SMS messaging capabilities.

## Authentication

All Project API endpoints require authentication. See the [Authentication](../authentication.md) section for details.

## Endpoints

### Get Project Details

```http
GET /api/project/details
```

Retrieve details about your project.

#### Response

A JSON object containing the project details, including name, description, status, and other relevant information.

#### Example Response

```json
{
  "success": true,
  "data": {
    "project_id": "proj_1a2b3c4d5e",
    "name": "E-commerce SMS Notifications",
    "description": "SMS notification system for order updates and delivery tracking",
    "status": "active",
    "created_at": "2023-01-15T10:30:00Z",
    "owner": {
      "name": "Tech Solutions Inc.",
      "email": "<EMAIL>",
      "phone": "+2519876543210"
    },
    "features": [
      "SMS Messaging",
      "OTP Verification",
      "Bulk Messaging",
      "Template Support"
    ]
  }
}
```

### Get Project Balance

```http
GET /api/project/balance
```

Retrieve the current balance of your project.

#### Response

A JSON object containing the project's balance information.

#### Example Response

```json
{
  "success": true,
  "data": {
    "balance": 1250.75,
    "currency": "USD",
    "last_updated": "2023-06-15T09:45:22Z",
    "subscription": {
      "plan": "Professional",
      "renewal_date": "2023-07-15T00:00:00Z",
      "sms_remaining": 3245,
      "sms_total": 5000
    }
  }
}
```

### Get Project Contacts

```http
GET /api/project/contacts/{group_id}
```

Retrieve contacts from a specific contact group in your project.

#### Path Parameters

| Parameter | Type   | Required | Description |
|-----------|--------|----------|-------------|
| group_id  | string | Yes      | The ID of the contact group |

#### Example Response

```json
{
  "success": true,
  "data": {
    "group_id": "grp_9a8b7c6d5e",
    "group_name": "Customers",
    "contacts": [
      {
        "id": "cnt_1a2b3c4d5e",
        "name": "John Doe",
        "phone": "+2519123456789",
        "email": "<EMAIL>",
        "tags": ["vip", "active"]
      },
      {
        "id": "cnt_2b3c4d5e6f",
        "name": "Jane Smith",
        "phone": "+2519876543210",
        "email": "<EMAIL>",
        "tags": ["active"]
      }
    ],
    "total_contacts": 2
  }
}
```

#### Query Parameters

| Parameter | Type   | Required | Description |
|-----------|--------|----------|-------------|
| page      | int    | No       | Page number for pagination |
| per_page  | int    | No       | Number of items per page |
| from      | date   | No       | Start date filter |
| to        | date   | No       | End date filter |

#### Response

A JSON object containing a paginated list of contacts in the specified group.

### Get Project Consumptions

```http
GET /api/project/consumptions
```

Retrieve the consumption history of your project.

#### Query Parameters

| Parameter | Type   | Required | Description |
|-----------|--------|----------|-------------|
| page      | int    | No       | Page number for pagination |
| per_page  | int    | No       | Number of items per page |
| from      | date   | No       | Start date filter |
| to        | date   | No       | End date filter |

#### Response

A JSON object containing a paginated list of consumption records, showing how your project has used its resources.

## Project Status

A project can have one of the following statuses:

- **active**: The project is active and can use the API
- **inactive**: The project is inactive and cannot use the API
- **suspended**: The project has been suspended and cannot use the API

## Project Types

Projects can be of different types, which may affect their capabilities and pricing:

- **standard**: Standard project with basic features
- **premium**: Premium project with advanced features
- **enterprise**: Enterprise project with custom features and support

## Error Handling

See the [Error Handling](../errors.md) section for details on error responses.

Common errors specific to project operations include:

- Project not found
- Project not active
- Insufficient balance
- Contact group not found