# OTP API

## Overview

The OTP (One-Time Password) API allows you to initiate and verify one-time passwords through the Lamah SMS Gateway. This is commonly used for user verification, secure authentication, and transaction confirmation.

## Authentication

All OTP API endpoints require authentication. See the [Authentication](../authentication.md) section for details.

## Rate Limiting

OTP endpoints are subject to rate limiting to prevent abuse. See the [Rate Limiting](../guides/rate-limiting.md) guide for more information.

## Endpoints

### Initiate OTP

```http
POST /api/otp/initiate
```

Generate and send an OTP to a recipient.

#### Request Body

| Parameter    | Type   | Required | Description |
|--------------|--------|----------|--------------|
| lang         | string | Yes      | Language code: 'ar' or 'en' |
| length       | int    | Yes      | OTP length: 4 or 6 digits |
| expiration   | int    | Yes      | Expiration time in minutes: 1, 5, or 10 |
| sender       | string | Yes      | The sender ID |
| message_type | string | No       | Message type: 'flash' or 'sms' (default: 'sms') |
| payment_type | string | Yes      | Payment type: 'wallet' or 'subscription' |
| receiver     | string | Yes      | The recipient's phone number |

#### Example Request

```json
{
  "lang": "en",
  "length": 6,
  "expiration": 5,
  "sender": "LamahOTP",
  "message_type": "sms",
  "payment_type": "wallet",
  "receiver": "+2519123456789"
}
```

#### Example Response

```json
{
  "success": true,
  "message": "OTP sent successfully",
  "data": {
    "request_id": "otp_9e8d7c6b5a4f3",
    "cost": 1
  }
}
```

#### Response Format

```json
{
  "request_id": "string",
  "cost": "integer"
}
```

- **request_id**: A unique identifier for the OTP request, used for verification
- **cost**: The cost of sending the OTP message

### Verify OTP

```http
POST /api/otp/verify
```

Verify an OTP that was previously sent to a recipient.

#### Request Body

| Parameter  | Type   | Required | Description |
|------------|--------|----------|--------------|
| request_id | string | Yes      | The request ID received from the initiate OTP call |
| otp        | string | Yes      | The OTP code entered by the user |

#### Example Request

```json
{
  "request_id": "otp_9e8d7c6b5a4f3",
  "otp": "123456"
}
```

#### Example Response

```json
{
  "success": true,
  "message": "OTP verified successfully",
  "data": {
    "verified": true,
    "timestamp": "2023-06-15T14:35:22Z"
  }
}
```

#### Request Body

| Parameter  | Type   | Required | Description |
|------------|--------|----------|-------------|
| request_id | string | Yes      | The request ID received from the initiate OTP endpoint |
| code       | string | Yes      | The OTP code to verify |

#### Response

```json
{
  "message": "string"
}
```

- **message**: A success message if the OTP is valid, or an error message if it's invalid or expired

## OTP Configuration

### Length Options

- **4 digits**: Shorter, easier to remember
- **6 digits**: More secure, harder to guess

### Expiration Options

- **1 minute**: High security, short window
- **5 minutes**: Balanced security and user convenience
- **10 minutes**: Extended window for user convenience

### Language Options

- **ar**: Arabic language for the OTP message
- **en**: English language for the OTP message

## Best Practices

1. **Security**: Use 6-digit OTPs with shorter expiration times for sensitive operations
2. **User Experience**: Consider the user's context when setting expiration times
3. **Localization**: Use the appropriate language based on the user's preferences
4. **Rate Limiting**: Implement client-side throttling to prevent hitting rate limits

## Error Handling

See the [Error Handling](../errors.md) section for details on error responses.

Common errors specific to OTP operations include:

- Invalid OTP code
- Expired OTP code
- Rate limit exceeded
- Invalid request ID