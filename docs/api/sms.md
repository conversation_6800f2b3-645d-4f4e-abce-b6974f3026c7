# SMS API

## Overview

The SMS API allows you to send SMS messages through the Lamah SMS Gateway. You can send individual messages, bulk messages to multiple recipients, messages to contact groups, and messages using templates.

## Authentication

All SMS API endpoints require authentication. See the [Authentication](../authentication.md) section for details.

## Endpoints

### Get All Messages

```http
GET /api/sms/messages
```

Retrieve a list of all messages sent through your project.

#### Query Parameters

| Parameter | Type   | Required | Description |
|-----------|--------|----------|-------------|
| page      | int    | No       | Page number for pagination |
| per_page  | int    | No       | Number of items per page |
| from      | date   | No       | Start date filter |
| to        | date   | No       | End date filter |

#### Response

A JSON object containing a paginated list of messages.

### Send a Message

```http
POST /api/sms/messages
```

Send an SMS message to a single recipient.

#### Request Body

| Parameter    | Type   | Required | Description |
|--------------|--------|----------|-------------|
| message      | string | Yes      | The message content |
| sender       | string | Yes      | The sender ID |
| message_type | string | No       | Message type: 'flash' or 'sms' (default: 'sms') |
| payment_type | string | Yes      | Payment type: 'wallet' or 'subscription' |
| receiver     | string | Yes      | The recipient's phone number |

#### Example Request

```json
{
  "message": "Your order #12345 has been confirmed and will be shipped tomorrow.",
  "sender": "LamahSMS",
  "message_type": "sms",
  "payment_type": "wallet",
  "receiver": "+2519123456789"
}
```

#### Example Response

```json
{
  "success": true,
  "message": "SMS sent successfully",
  "data": {
    "message_id": "msg_7f4b8a12e3c9d5",
    "cost": 1,
    "status": "sent",
    "timestamp": "2023-06-15T14:30:45Z"
  }
}
```

#### Response

A JSON object containing the message details and status.

### Send a Bulk Message

```http
POST /api/sms/messages/bulk
```

Send an SMS message to multiple recipients.

#### Request Body

| Parameter    | Type     | Required | Description |
|--------------|----------|----------|-------------|
| message      | string   | Yes      | The message content |
| sender       | string   | Yes      | The sender ID |
| message_type | string   | No       | Message type: 'flash' or 'sms' (default: 'sms') |
| payment_type | string   | Yes      | Payment type: 'wallet' or 'subscription' |
| receivers    | array    | Yes      | Array of recipient phone numbers |

#### Response

A JSON object containing the message details and status.

### Send a Message to Contacts

```http
POST /api/sms/messages/contacts
```

Send an SMS message to a contact group.

#### Request Body

| Parameter        | Type   | Required | Description |
|------------------|--------|----------|-------------|
| message          | string | Yes      | The message content |
| sender           | string | Yes      | The sender ID |
| message_type     | string | No       | Message type: 'flash' or 'sms' (default: 'sms') |
| payment_type     | string | Yes      | Payment type: 'wallet' or 'subscription' |
| contact_group_id | string | Yes      | UUID of the contact group |

#### Response

A JSON object containing the message details and status.

### Get a Message by ID

```http
GET /api/sms/messages/{message_id}
```

Retrieve details of a specific message by its ID.

#### Path Parameters

| Parameter  | Type   | Required | Description |
|------------|--------|----------|-------------|
| message_id | string | Yes      | The ID of the message to retrieve |

#### Response

A JSON object containing the message details.

### Send a Message Using Template

```http
POST /api/sms/messages/template
```

Send an SMS message using a predefined template.

#### Request Body

| Parameter    | Type   | Required | Description |
|--------------|--------|----------|-------------|
| template_id  | string | Yes      | The ID of the template to use |
| sender       | string | Yes      | The sender ID |
| message_type | string | No       | Message type: 'flash' or 'sms' (default: 'sms') |
| payment_type | string | Yes      | Payment type: 'wallet' or 'subscription' |
| receiver     | string | Yes      | The recipient's phone number |
| params       | array  | Yes      | Parameters to replace placeholders in the template |

#### Response

A JSON object containing the message details and status.

## Message Types

- **SMS**: Standard SMS message
- **Flash**: Flash SMS message that appears directly on the recipient's screen without being stored in their inbox

## Payment Types

- **Wallet**: Deduct the cost from your wallet balance
- **Subscription**: Deduct the cost from your subscription balance

## Error Handling

See the [Error Handling](../errors.md) section for details on error responses.