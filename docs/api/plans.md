# Plans API

## Overview

The Plans API allows users to retrieve information about available subscription plans for the Lamah SMS Gateway service. This endpoint is useful for users who want to explore pricing options before registering or for existing users who want to upgrade their subscription.

## Authentication

This endpoint does not require authentication and is publicly accessible.

## Endpoints

### Get Plans List

```http
GET /api/plans
```

Retrieve a list of available subscription plans.

#### Response

A JSON object containing an array of available plans, including details such as name, description, features, and pricing.

#### Example Response

```json
{
  "success": true,
  "data": {
    "plans": [
      {
        "id": "plan_basic",
        "name": "Basic",
        "description": "Entry-level plan for small businesses",
        "price": 29.99,
        "currency": "USD",
        "billing_cycle": "monthly",
        "features": [
          "1,000 SMS messages per month",
          "OTP support",
          "Standard support"
        ]
      },
      {
        "id": "plan_professional",
        "name": "Professional",
        "description": "Mid-tier plan for growing businesses",
        "price": 99.99,
        "currency": "USD",
        "billing_cycle": "monthly",
        "features": [
          "5,000 SMS messages per month",
          "OTP support",
          "Bulk messaging",
          "Template support",
          "Priority support"
        ]
      }
    ]
  }
}
```

## Plan Types

The Lamah SMS Gateway offers various plan types to suit different needs:

- **Basic**: Entry-level plans for small businesses or individuals
- **Professional**: Mid-tier plans for growing businesses
- **Enterprise**: High-volume plans for large organizations

## Plan Features

Plans may include various features, such as:

- **SMS Volume**: Number of SMS messages included in the plan
- **OTP Support**: Ability to send and verify OTPs
- **Bulk Messaging**: Support for sending messages to multiple recipients
- **Template Support**: Ability to create and use message templates
- **API Access**: Number of API calls allowed per day/month
- **Support Level**: Level of customer support provided

## Pricing Models

Plans may use different pricing models:

- **Subscription-based**: Fixed monthly or annual fee
- **Pay-as-you-go**: Pay only for what you use
- **Hybrid**: Combination of subscription and pay-as-you-go

## Error Handling

See the [Error Handling](../errors.md) section for details on error responses.

Common errors specific to plan operations include:

- Service unavailable
- Rate limit exceeded