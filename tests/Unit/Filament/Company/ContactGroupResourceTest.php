<?php

declare(strict_types=1);

use App\Filament\Company\Resources\ContactGroupResource;
use App\Models\Company;
use App\Models\Contact;
use App\Models\ContactGroup;
use App\Models\User;
use Filament\Facades\Filament;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\File;
use Illuminate\Support\Facades\Storage;

use function Pest\Laravel\actingAs;
use function Pest\Laravel\assertDatabaseHas;
use function Pest\Laravel\get;
use function Pest\Laravel\seed;
use function Pest\Livewire\livewire;

beforeEach(function (): void {
    seed();
    Filament::setCurrentPanel(Filament::getPanel('company'));

    $this->company = Company::factory()->create();
    $this->user = User::factory()->create();
    $this->company->users()->attach($this->user);
});

it('can render page with right permissions', function () {
    get(
        ContactGroupResource::getUrl('index', ['tenant' => $this->company->id]),
    )->assertRedirect(Filament::getPanel('company')->getLoginUrl());

    actingAs($this->user);
    get(
        ContactGroupResource::getUrl('index', ['tenant' => $this->company->id]),
    )->assertForbidden();

    actingAs($this->user->assignRole('company_owner'));
    get(
        ContactGroupResource::getUrl('index', ['tenant' => $this->company->id]),
    )->assertOk();
});

it('shows empty state when company has no contact groups', function () {
    actingAs($this->user->assignRole('company_owner'));
    Filament::setTenant($this->company);
    livewire(ContactGroupResource\Pages\ListContactGroups::class)->assertSee(
        __('No contact groups found'),
    );
});

it('can list contact groups company', function () {
    $contactGroups = ContactGroup::factory()
        ->count(5)
        ->create(['company_id' => $this->company->id]);

    actingAs($this->user->assignRole('company_owner'));
    Filament::setTenant($this->company);
    livewire(ContactGroupResource\Pages\ListContactGroups::class, [
        'record' => $contactGroups,
    ])->assertCanSeeTableRecords($contactGroups);
});

it('can render create page with right permissions', function () {
    get(
        ContactGroupResource::getUrl('create', ['tenant' => $this->company->id]),
    )->assertRedirect(Filament::getPanel('company')->getLoginUrl());

    actingAs($this->user);
    get(
        ContactGroupResource::getUrl('create', ['tenant' => $this->company->id]),
    )->assertForbidden();

    actingAs($this->user->assignRole('company_owner'));
    get(
        ContactGroupResource::getUrl('create', ['tenant' => $this->company->id]),
    )->assertOk();
});

it('can create contact groups company', function () {
    $contactGroups = ContactGroup::factory()->make();

    actingAs($this->user->assignRole('company_owner'));
    Filament::setTenant($this->company);

    livewire(ContactGroupResource\Pages\CreateContactGroup::class)
        ->fillForm([
            'name' => $contactGroups->name,
            'status' => $contactGroups->status,
            'reference' => $contactGroups->reference,
            'description' => $contactGroups->description,
        ])
        ->call('create')
        ->assertHasNoFormErrors();

    assertDatabaseHas(ContactGroup::class, [
        'name' => $contactGroups->name,
        'status' => $contactGroups->status,
        'reference' => $contactGroups->reference,
        'description' => $contactGroups->description,
        'company_id' => $this->company->id,
    ]);
});

it('can render edit page with right permissions', function () {
    $contactGroups = ContactGroup::factory()->create(['company_id' => $this->company->id]);

    get(
        ContactGroupResource::getUrl('edit', [
            'tenant' => $this->company->id,
            'record' => $contactGroups,
        ]),
    )->assertRedirect(Filament::getPanel('company')->getLoginUrl());

    actingAs($this->user);
    get(
        ContactGroupResource::getUrl('edit', [
            'tenant' => $this->company->id,
            'record' => $contactGroups,
        ]),
    )->assertForbidden();

    actingAs($this->user->assignRole('company_owner'));
    get(
        ContactGroupResource::getUrl('edit', [
            'tenant' => $this->company->id,
            'record' => $contactGroups,
        ]),
    )->assertOk();
    get(
        ContactGroupResource::getUrl('edit', [
            'tenant' => $this->company->id,
            'record' => 1,
        ]),
    )->assertNotFound();
});

it('can update contact groups company', function () {
    $contactGroups = ContactGroup::factory()
        ->create(['company_id' => $this->company->id]);
    $newContactGroups = ContactGroup::factory()
        ->create(['company_id' => $this->company->id]);

    actingAs(User::factory()->create()->assignRole('company_owner'));
    Filament::setTenant($this->company);
    livewire(ContactGroupResource\Pages\EditContactGroup::class, [
        'record' => $contactGroups->id,
    ])
        ->fillForm([
            'name' => $newContactGroups->name,
            'status' => $newContactGroups->status,
            'reference' => $newContactGroups->reference,
            'description' => $newContactGroups->description,
        ])
        ->call('save')
        ->assertHasNoFormErrors();

    expect($contactGroups->refresh())
        ->name->toEqual($newContactGroups->name)
        ->status->toEqual($newContactGroups->status)
        ->reference->toEqual($newContactGroups->reference)
        ->description->toEqual($newContactGroups->description);
});

it('can render contacts page in contact-groups with right permissions', function () {
    $contactGroups = ContactGroup::factory()->create(['company_id' => $this->company->id]);

    get(
        ContactGroupResource::getUrl('contacts', [
            'tenant' => $this->company->id,
            'record' => $contactGroups->id]),
    )->assertRedirect(Filament::getPanel('company')->getLoginUrl());

    actingAs($this->user);
    get(
        ContactGroupResource::getUrl('contacts', [
            'tenant' => $this->company->id,
            'record' => $contactGroups->id]),
    )->assertForbidden();

    actingAs($this->user->assignRole('company_owner'));
    get(
        ContactGroupResource::getUrl('contacts', [
            'tenant' => $this->company->id,
            'record' => $contactGroups->id]),
    )->assertOk();
});

it('shows empty state when contact-groups has no contact', function () {
    $contactGroups = ContactGroup::factory()->create(['company_id' => $this->company->id]);

    actingAs($this->user->assignRole('company_owner'));
    Filament::setTenant($this->company);
    livewire(ContactGroupResource\Pages\ManageContacts::class, [
        'record' => $contactGroups->id,
    ])->assertSee(__('No contacts found'));
});

it('can list contact when contact-group has contacts ', function () {
    $contactGroups = ContactGroup::factory()
        ->create(['company_id' => $this->company->id]);

    $contacts = Contact::factory()
        ->count(5)
        ->create(['contact_group_id' => $contactGroups->id]);

    actingAs($this->user->assignRole('company_owner'));
    Filament::setTenant($this->company);
    livewire(ContactGroupResource\Pages\ManageContacts::class, [
        'record' => $contactGroups->id,
    ])->assertCanSeeTableRecords($contacts);
});

it('can delete contact groups company', function () {
    $contactGroups = ContactGroup::factory()
        ->create(['company_id' => $this->company->id]);

    actingAs($this->user->assignRole('company_owner'));
    Filament::setTenant($this->company);

    livewire(ContactGroupResource\Pages\EditContactGroup::class,
        ['record' => $contactGroups->id])
        ->callAction('delete')
        ->assertHasNoErrors()
        ->assertNotified(__('filament-actions::delete.single.notifications.deleted.title'));
});

it('can create contact to contact group', function () {
    $contactGroups = ContactGroup::factory()
        ->create(['company_id' => $this->company->id]);

    actingAs($this->user->assignRole('company_owner'));
    Filament::setTenant($this->company);

    $newContact = Contact::factory()->make();

    livewire(ContactGroupResource\Pages\ManageContacts::class, [
        'record' => $contactGroups->id,
    ])->callTableAction('create', data: [
        'name' => $newContact->name,
        'phone' => $newContact->phone,
        'sex' => $newContact->sex,
        'city' => $newContact->city,
    ])
        ->assertHasNoTableActionErrors();
});

it('can delete contact from contact group', function () {
    $contactGroups = ContactGroup::factory()
        ->create(['company_id' => $this->company->id]);

    $contact = Contact::factory()
        ->create(['contact_group_id' => $contactGroups->id]);

    actingAs($this->user->assignRole('company_owner'));
    Filament::setTenant($this->company);

    livewire(ContactGroupResource\Pages\ManageContacts::class, [
        'record' => $contactGroups->id,
    ])->callTableAction('delete', $contact->id)
        ->assertHasNoTableActionErrors();
});

it('can view edit contact', function () {
    $contactGroups = ContactGroup::factory()
        ->create(['company_id' => $this->company->id]);
    $contact = Contact::factory()
        ->create(['contact_group_id' => $contactGroups->id]);

    actingAs($this->user->assignRole('company_owner'));
    Filament::setTenant($this->company);

    livewire(ContactGroupResource\Pages\ManageContacts::class, [
        'record' => $contactGroups->id,
    ])->callTableAction('edit', $contact->id)
        ->assertHasNoTableActionErrors();
});

it('can import contacts from CSV file', function () {

    $contactGroup = ContactGroup::factory()->create([
        'company_id' => $this->company->id,
    ]);

    actingAs($this->user->assignRole('company_owner'));
    Filament::setTenant($this->company);

    // Create real temp file with correct headers
    $csvPath = tempnam(sys_get_temp_dir(), 'contacts_');
    file_put_contents($csvPath, <<<'CSV'
name,phone,sex,births,city
John Doe,00218911234567,male,1990-01-01,Tripoli
Jane Smith,00218911234568,female,1985-05-10,Benghazi
CSV);

    $uploadedFile = new UploadedFile(
        $csvPath,
        'contacts.csv',
        'text/csv',
        null,
        true
    );

    // Store the file and get the path
    $storedPath = $uploadedFile->store('', 'public');

    // Debug if needed
    expect(Storage::disk('public')->exists($storedPath))->toBeTrue();

    // Convert the stored path to an array format that Filament expects
    $attachmentData = [$storedPath];

    livewire(ContactGroupResource\Pages\ManageContacts::class, [
        'record' => $contactGroup->id,
    ])
        ->callTableAction('import', data: [
            'attachment' => $attachmentData,
        ])
        ->assertHasNoTableActionErrors();

    $this->assertDatabaseHas('contacts', [
        'name' => 'John Doe',
        'city' => 'Tripoli',
    ]);

    $this->assertDatabaseHas('contacts', [
        'name' => 'Jane Smith',
        'city' => 'Benghazi',
    ]);
});

it('can not import contacts from CSV file', function () {

    $contactGroup = ContactGroup::factory()->create([
        'company_id' => $this->company->id,
    ]);

    actingAs($this->user->assignRole('company_owner'));
    Filament::setTenant($this->company);

    // Create real temp file with correct headers
    $csvPath = tempnam(sys_get_temp_dir(), 'contacts_');
    file_put_contents($csvPath, <<<'CSV'
name,phone,sex,births,city
John Doe,00218911234567,Male,1990-01-01,Tripoli
Jane Smith,00218911234568,Female,1985-05-10,Benghazi
CSV);

    $uploadedFile = new UploadedFile(
        $csvPath,
        'contacts.csv',
        'text/csv',
        null,
        true
    );

    // Store the file and get the path
    $storedPath = $uploadedFile->store('', 'public');

    // Debug if needed
    expect(Storage::disk('public')->exists($storedPath))->toBeTrue();

    // Convert the stored path to an array format that Filament expects
    $attachmentData = [$storedPath];

    livewire(ContactGroupResource\Pages\ManageContacts::class, [
        'record' => $contactGroup->id,
    ])
        ->callTableAction('import', data: [
            'attachment' => $attachmentData,
        ])
        ->assertNotified()
        ->assertHasNoTableActionErrors();

});
